# To-Do Liste: ESP32 Lüftungssystem - Code Refaktorierung

## 🎯 Hauptziel: main.cpp entlasten (259 Zeilen → ~50 Zeilen)

---

## 📁 1. Sensor-Modul erstellen (`sensor_management.h/.cpp`)

### 1.1 Neue Dateien erstellen

- [ ] `include/sensor_management.h` - Header-Datei
- [ ] `src/sensor_management.cpp` - Implementation

### 1.2 SensorManager Klasse definieren

```cpp
class SensorManager {
private:
    Adafruit_BME280 bme;
    // Indoor sensor
    float indoorTemp = 0;
    float indoorHumidity = 0;
    bool indoorSensorConnected = false;
    
    // Outdoor sensor
    float lastOutdoorTemp = NAN;
    float lastOutdoorHumidity = NAN;
    bool outdoorSensorDefekt = false;
    unsigned long lastOutdoorUpdate = 0;
    
    // Taupunkt-Berechnungen
    float taupunktInnen = 0;
    float taupunktAussen = 0;
    bool lueftungEmpfehlung = false;

public:
    bool initIndoorSensor(int sdaPin, int sclPin);
    void updateIndoorSensor();
    void updateOutdoorSensor(float temp, float humidity);
    SensorData getIndoorData();
    SensorData getOutdoorData();
    bool shouldVentilate();
    bool isIndoorConnected();
    bool isOutdoorConnected();
    bool isOutdoorSensorDefekt();
    void logSensorStatus();
};
```

### 1.3 Taupunkt-Funktionen integrieren

- [ ] `taupunkt()` Funktion aus `taupunkt_in.cpp` übernehmen
- [ ] `sollGelueftetWerden()` Funktion übernehmen
- [ ] Alle Konstanten (SCHALTmin, HYSTERESE, etc.) übernehmen

### 1.4 BME280-Code aus main.cpp verschieben

- [ ] I2C-Initialisierung
- [ ] Sensor-Erkennung (0x76, 0x77)
- [ ] Plausibilitätsprüfung (>100°C)
- [ ] Automatische Wiedererkennung (alle 30s)

---

## 📁 2. Konfigurations-Modul (`config.h`)

### 2.1 Neue Datei erstellen

- [ ] `include/config.h` - Alle Konstanten sammeln

### 2.2 Magic Numbers eliminieren

```cpp
// Hardware-Konfiguration
#define SDA_PIN 12
#define SCL_PIN 13
#define LCD_MOSI 23
#define LCD_SCLK 18
#define LCD_CS 15
#define LCD_DC 2
#define LCD_RST 4
#define LCD_BLK 32

// Sensor-Konfiguration
#define MAX_TEMPERATURE_THRESHOLD 100.0
#define MIN_TEMPERATURE_THRESHOLD -50.0
#define OUTDOOR_TIMEOUT 15000
#define SENSOR_RETRY_INTERVAL 30000

// Taupunkt-Konfiguration
#define SCHALTmin 5.0
#define HYSTERESE 1.0
#define TEMP1_min 10.0
#define TEMP2_min -10.0

// Timing-Konfiguration
#define LOG_INTERVAL 10000
#define LOOP_DELAY 5000

// WiFi-Konfiguration
#define WIFI_SSID "Freifunk"
#define WIFI_PASSWORD ""
#define MDNS_HOSTNAME "esp-inside"
```

---

## 📁 3. Web-Server-Modul (`web_server.h/.cpp`)

### 3.1 Neue Dateien erstellen

- [ ] `include/web_server.h`
- [ ] `src/web_server.cpp`

### 3.2 WebServerManager Klasse

```cpp
class WebServerManager {
private:
    WebServer server;
    std::function<void(float, float)> outdoorDataCallback;
    
public:
    void setupServer();
    void handleClient();
    void setOutdoorDataCallback(std::function<void(float, float)> callback);
    void startMDNS();
    
private:
    void handleSensorEndpoint();
};
```

### 3.3 Code aus main.cpp verschieben

- [ ] `handleSensor()` Funktion
- [ ] `/sensor` Endpoint-Handler
- [ ] mDNS-Setup
- [ ] WiFi-Verbindung

---

## 📁 4. Logging-Modul (`logger.h/.cpp`)

### 4.1 Neue Dateien erstellen

- [ ] `include/logger.h`
- [ ] `src/logger.cpp`

### 4.2 Logger Klasse

```cpp
class Logger {
private:
    NTPClient timeClient;
    WiFiUDP ntpUDP;
    unsigned long lastLogTime;
    
public:
    void init();
    void update();
    void logSensorData(const SensorData& indoor, const SensorData& outdoor);
    void logStatus(const char* message);
    void logError(const char* error);
    
private:
    String getTimestamp();
    void printSeparator();
};
```

### 4.3 Code aus main.cpp verschieben

- [ ] `logSensorData()` Funktion
- [ ] NTP-Client-Setup
- [ ] Zeitstempel-Logik
- [ ] Strukturierte Ausgabe

---

## 📁 5. Display-Modul erweitern (`display_in.h/.cpp`)

### 5.1 Bestehende Dateien erweitern

- [ ] `updateDisplay()` Funktion vereinfachen
- [ ] Sensor-Status-Logik auslagern
- [ ] Fehlermeldungen zentralisieren

---

## 🔧 6. main.cpp Refaktorierung

### 6.1 Neue Struktur

```cpp
#include <Arduino.h>
#include "config.h"
#include "sensor_management.h"
#include "web_server.h"
#include "logger.h"
#include "display_in.h"

// Globale Manager-Instanzen
SensorManager sensorManager;
WebServerManager webServerManager;
Logger logger;
Adafruit_ST7789 lcd(LCD_CS, LCD_DC, LCD_RST);

void setup() {
    Serial.begin(115200);
    
    // Hardware initialisieren
    sensorManager.initIndoorSensor(SDA_PIN, SCL_PIN);
    initDisplay(lcd);
    
    // WiFi und Server
    webServerManager.setupServer();
    webServerManager.startMDNS();
    
    // Logging
    logger.init();
    
    Serial.println("✓ System gestartet");
}

void loop() {
    // Web-Requests bearbeiten
    webServerManager.handleClient();
    
    // Sensoren aktualisieren
    sensorManager.updateIndoorSensor();
    
    // Display aktualisieren
    updateDisplay(lcd, sensorManager.getIndoorData(), 
                  sensorManager.getOutdoorData(), 
                  sensorManager.shouldVentilate());
    
    // Logging
    logger.update();
    
    delay(LOOP_DELAY);
}
```

### 6.2 Code entfernen aus main.cpp

- [ ] Alle Sensor-Variablen entfernen
- [ ] BME280-Initialisierung entfernen
- [ ] `handleSensor()` entfernen
- [ ] `logSensorData()` entfernen
- [ ] WiFi-Setup entfernen
- [ ] mDNS-Setup entfernen
- [ ] Alle Magic Numbers entfernen

---

## 🧹 7. Aufräumen

### 7.1 Alte Dateien

- [ ] `taupunkt_in.h` → in `sensor_management.h` integrieren
- [ ] `taupunkt_in.cpp` → in `sensor_management.cpp` integrieren
- [ ] `async.h` und `async.cpp` → prüfen ob noch benötigt

### 7.2 Includes anpassen

- [ ] Alle `#include` Statements in `main.cpp` aktualisieren
- [ ] `platformio.ini` prüfen (alle Bibliotheken noch benötigt?)

---

## ✅ 8. Testen und Validierung

### 8.1 Kompilierung

- [ ] `pio run` - Kompilierung ohne Fehler
- [ ] Alle Warnings beheben
- [ ] Code-Größe prüfen (nicht zu groß geworden?)

### 8.2 Funktionalität

- [ ] BME280-Sensor funktioniert
- [ ] Außensensor-Daten werden empfangen
- [ ] Taupunkt-Berechnung korrekt
- [ ] Lüftungsempfehlung funktioniert
- [ ] Display zeigt alle Daten
- [ ] Web-Server antwortet
- [ ] Logging funktioniert
- [ ] mDNS funktioniert

### 8.3 Performance

- [ ] Loop-Zeit messen (< 5s?)
- [ ] Speicherverbrauch prüfen
- [ ] WiFi-Stabilität testen

---

## 📊 9. Dokumentation

### 9.1 Code-Dokumentation

- [ ] Alle neuen Funktionen dokumentieren
- [ ] Klassen-Diagramm erstellen
- [ ] API-Dokumentation schreiben

### 9.2 README aktualisieren

- [ ] Neue Modulstruktur dokumentieren
- [ ] Setup-Anleitung aktualisieren
- [ ] Troubleshooting-Guide

---

## 🎯 10. Erweiterte Features (optional)

### 10.1 Web-Interface

- [ ] Konfigurations-Webseite
- [ ] Live-Daten-Dashboard
- [ ] Sensor-Kalibrierung

### 10.2 Datenlogger

- [ ] SD-Karten-Support
- [ ] SPIFFS für Daten
- [ ] CSV-Export

### 10.3 Benachrichtigungen

- [ ] Push-Notifications
- [ ] E-Mail-Alerts
- [ ] Telegram-Bot

---

**Ziel: main.cpp von 259 Zeilen auf ~50 Zeilen reduzieren!**
