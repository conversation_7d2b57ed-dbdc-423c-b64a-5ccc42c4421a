#include <Arduino.h>
#include <WiFi.h>
#include <ESPmDNS.h>
#include <Wire.h>
#include <Adafruit_Sensor.h>
#include <Adafruit_BME280.h>
#include <HTTPClient.h>
#include <ArduinoOTA.h>
// I2C Pins für ESP32-C3
#define SDA_PIN 3
#define SCL_PIN 4

const char* ssid = "Freifunk";
const char* password = "";

// OTA Konfiguration
const char* otaHostname = "esp-outdoor";
const char* otaPassword = "ota123";

// BME280 Sensor
Adafruit_BME280 bme;

// Messwerte
float temperature = 0;
float humidity = 0;
bool sensorConnected = false;

const char* receiverHost = "http://esp-inside.local"; // mDNS für Freifunk DHCP

// LED-Funktionen entfernt - ESP32-C3 hat keine eingebaute LED

void setup() {
  Serial.begin(115200);
  delay(1000);

  Serial.println("✓ ESP32-C3 initialisiert (keine LED)");

  // I2C konfigurieren
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(100000); // 100kHz für stabilere Kommunikation
  Serial.printf("I2C konfiguriert: SDA=%d, SCL=%d, Clock=100kHz\n", SDA_PIN, SCL_PIN);

  // BME280 initialisieren
  if (bme.begin(0x76)) {
    sensorConnected = true;
    Serial.printf("BME280 gefunden! Sensor ID: 0x%02X\n", bme.sensorID());
  } else {
    sensorConnected = false;
    Serial.println("BME280 Sensor nicht gefunden! Verwende Standardwerte.");
  }

  // WLAN verbinden
  WiFi.begin(ssid, password);
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }
  Serial.println("\nVerbunden: " + WiFi.localIP().toString());

  // mDNS starten (wichtig für Freifunk DHCP)
  if (!MDNS.begin("esp-outdoor")) {
    Serial.println("✗ Fehler bei mDNS");
  } else {
    Serial.println("✓ mDNS gestartet: esp-outdoor.local");
  }

  // OTA konfigurieren
  ArduinoOTA.setHostname(otaHostname);
  ArduinoOTA.setPassword(otaPassword);
  
  ArduinoOTA.onStart([]() {
    String type = (ArduinoOTA.getCommand() == U_FLASH) ? "sketch" : "filesystem";
    Serial.println("OTA Update gestartet: " + type);
  });
  
  ArduinoOTA.onEnd([]() {
    Serial.println("\nOTA Update abgeschlossen");
  });
  
  ArduinoOTA.onProgress([](unsigned int progress, unsigned int total) {
    Serial.printf("OTA Progress: %u%%\r", (progress / (total / 100)));
  });
  
  ArduinoOTA.onError([](ota_error_t error) {
    Serial.printf("OTA Fehler[%u]: ", error);
    if (error == OTA_AUTH_ERROR) Serial.println("Auth Failed");
    else if (error == OTA_BEGIN_ERROR) Serial.println("Begin Failed");
    else if (error == OTA_CONNECT_ERROR) Serial.println("Connect Failed");
    else if (error == OTA_RECEIVE_ERROR) Serial.println("Receive Failed");
    else if (error == OTA_END_ERROR) Serial.println("End Failed");
  });
  
  ArduinoOTA.begin();
  Serial.println("✓ OTA bereit - Hostname: " + String(otaHostname));
}

void sendData(float temp, float hum) {
  HTTPClient http;
  String url = String(receiverHost) + "/sensor?temp=" + String(temp, 2) + "&hum=" + String(hum, 2) + "&sensor=" + String(sensorConnected ? "ok" : "fehlt");
  
  http.begin(url);
  http.setTimeout(5000); // 5 Sekunden Timeout
  
  int httpCode = http.GET();
  
  if (httpCode > 0) {
    if (httpCode == HTTP_CODE_OK) {
      Serial.printf("✓ Daten gesendet: T=%.2f°C, H=%.2f%% (Code %d)\n", temp, hum, httpCode);
    } else {
      Serial.printf("⚠ HTTP Fehler: Code %d\n", httpCode);
    }
  } else {
    Serial.printf("✗ Verbindungsfehler: %s\n", http.errorToString(httpCode).c_str());
  }
  
  http.end();
}

void loop() {
  // OTA Updates verarbeiten
  ArduinoOTA.handle();
  
  if (sensorConnected) {
    temperature = bme.readTemperature();
    humidity = bme.readHumidity();

    // Alle 10 Sekunden senden
    static unsigned long lastSend = 0;
    if (millis() - lastSend > 10000) {
      Serial.printf("Messwerte: T=%.2f°C, H=%.2f%%\n", temperature, humidity);
      sendData(temperature, humidity);
      lastSend = millis();
      
      // Kurze Pause nach dem Senden
      delay(200);
    }
  } else {
    // Alle 30 Sekunden versuchen Sensor zu finden (OHNE I2C-Reinitialisierung)
    static unsigned long lastRetry = 0;
    if (millis() - lastRetry > 30000) {
      Serial.println("Teste BME280-Verbindung...");
      
      // Nur Sensor-Daten testen, NICHT bme.begin() wiederholen
      float testTemp = bme.readTemperature();
      if (!isnan(testTemp) && testTemp > -50.0 && testTemp < 100.0) {
        sensorConnected = true;
        Serial.println("✓ BME280 wieder erkannt!");
      } else {
        Serial.println("✗ BME280 immer noch nicht erreichbar");
      }
      lastRetry = millis();
    }
  }
  
  delay(100);
}