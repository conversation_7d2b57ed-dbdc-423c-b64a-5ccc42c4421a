#include "async.h"
#include "config.h"
#include "sensor.h"
#include <ArduinoJson.h>

void WebServerManager::setupServer() {
    // LittleFS starten
    startLittleFS();

    server.on("/", HTTP_GET, [this](AsyncWebServerRequest *request) { handleRootEndpoint(request); });
    server.on(SENSOR_ENDPOINT, HTTP_GET, [this](AsyncWebServerRequest *request) { handleSensorEndpoint(request); });
    server.on("/api/data", HTTP_GET, [this](AsyncWebServerRequest *request) { handleApiDataEndpoint(request); });
    server.onNotFound([this](AsyncWebServerRequest *request) { handleNotFound(request); });

    // Server starten
    server.begin();
    Serial.println("✓ AsyncWebServer gestartet");
}

void WebServerManager::handleClient() {
    // AsyncWebServer läuft automatisch im Hintergrund
    // Keine manuelle handleClient() Aufruf nötig
}

void WebServerManager::setOutdoorDataCallback(std::function<void(float, float)> callback) {
    outdoorDataCallback = callback;
}

void WebServerManager::setSensorManager(SensorManager* manager) {
    sensorManagerPtr = manager;
}

void WebServerManager::handleSensorEndpoint(AsyncWebServerRequest *request) {
    if (request->hasParam("temp") && request->hasParam("hum")) {
        float receivedTemp = request->getParam("temp")->value().toFloat();
        float receivedHumidity = request->getParam("hum")->value().toFloat();
        
        // Callback aufrufen (delegiert an SensorManager)
        if (outdoorDataCallback) {
            outdoorDataCallback(receivedTemp, receivedHumidity);
        }
        
        // Zusätzliche Sensor-Status-Info vom ESP32-C3 (falls vorhanden)
        if (request->hasParam("sensor")) {
            Serial.printf("ESP32-C3 Sensor-Status: %s\n", request->getParam("sensor")->value().c_str());
        }
        
        request->send(200, "text/plain", "OK");
    } else {
        request->send(400, "text/plain", "Bad Request");
        Serial.println("✗ Ungültige Sensor-Daten empfangen");
    }
}

void WebServerManager::handleRootEndpoint(AsyncWebServerRequest *request) {
    // HTML-Seite aus LittleFS laden
    if (LittleFS.exists("/index.html")) {
        request->send(LittleFS, "/index.html", "text/html");
        Serial.println("✓ HTML-Seite ausgeliefert");
    } else {
        // Fehler: HTML-Datei nicht gefunden
        request->send(404, "text/plain", "HTML-Datei nicht gefunden. LittleFS initialisieren.");
        Serial.println("✗ HTML-Datei /index.html nicht gefunden");
    }
}

void WebServerManager::handleApiDataEndpoint(AsyncWebServerRequest *request) {
    if (!sensorManagerPtr) {
        request->send(500, "application/json", "{\"error\":\"SensorManager nicht verfügbar\"}");
        return;
    }

    // Aktuelle Sensor-Daten abrufen
    SensorData indoorData = sensorManagerPtr->getIndoorData();
    SensorData outdoorData = sensorManagerPtr->getOutdoorData();
    bool shouldVentilate = sensorManagerPtr->shouldVentilate();

    // JSON-Dokument erstellen
    JsonDocument doc;

    // Indoor-Daten
    JsonObject indoor = doc["indoor"].to<JsonObject>();
    indoor["temperature"] = indoorData.temperature;
    indoor["humidity"] = indoorData.humidity;
    indoor["dewPoint"] = indoorData.dewPoint;
    indoor["connected"] = indoorData.connected;
    indoor["sensorDefect"] = indoorData.sensorDefect;

    // Outdoor-Daten
    JsonObject outdoor = doc["outdoor"].to<JsonObject>();
    outdoor["temperature"] = outdoorData.temperature;
    outdoor["humidity"] = outdoorData.humidity;
    outdoor["dewPoint"] = outdoorData.dewPoint;
    outdoor["connected"] = outdoorData.connected;
    outdoor["sensorDefect"] = outdoorData.sensorDefect;

    // Lüftungsempfehlung
    doc["shouldVentilate"] = shouldVentilate;

    // Zeitstempel
    doc["timestamp"] = millis();

    // JSON-String erstellen
    String jsonString;
    serializeJson(doc, jsonString);

    // CORS-Header hinzufügen für lokale Entwicklung
    AsyncWebServerResponse *response = request->beginResponse(200, "application/json", jsonString);
    response->addHeader("Access-Control-Allow-Origin", "*");
    request->send(response);

    Serial.println("✓ API-Daten ausgeliefert");
}

void WebServerManager::handleNotFound(AsyncWebServerRequest *request) {
    String message = "File Not Found\n\n";
    message += "URI: ";
    message += request->url();
    message += "\nMethod: ";
    message += (request->method() == HTTP_GET) ? "GET" : "POST";
    message += "\nArguments: ";
    message += request->args();
    message += "\n";
    
    for (uint8_t i = 0; i < request->args(); i++) {
        message += " " + request->argName(i) + ": " + request->arg(i) + "\n";
    }
    
    request->send(404, "text/plain", message);
}

void WebServerManager::startLittleFS() {
    if (!LittleFS.begin(true)) {
        Serial.println("✗ LittleFS Mount Failed");
        return;
    }
    
    Serial.println("✓ LittleFS Mounted Successfully");
    
    // LittleFS-Inhalt auflisten (Debug)
    File root = LittleFS.open("/");
    if (root) {
        Serial.println("LittleFS-Dateien:");
        File file = root.openNextFile();
        while (file) {
            Serial.printf("  %s (%d bytes)\n", file.name(), file.size());
            file = root.openNextFile();
        }
        root.close();
    }
}

String WebServerManager::getContentType(String filename) {
    if (filename.endsWith(".html")) return "text/html";
    else if (filename.endsWith(".css")) return "text/css";
    else if (filename.endsWith(".js")) return "application/javascript";
    else if (filename.endsWith(".json")) return "application/json";
    else if (filename.endsWith(".png")) return "image/png";
    else if (filename.endsWith(".jpg")) return "image/jpeg";
    else if (filename.endsWith(".gif")) return "image/gif";
    else if (filename.endsWith(".ico")) return "image/x-icon";
    else if (filename.endsWith(".xml")) return "text/xml";
    else if (filename.endsWith(".pdf")) return "application/x-pdf";
    else if (filename.endsWith(".zip")) return "application/x-zip";
    else if (filename.endsWith(".gz")) return "application/x-gzip";
    return "text/plain";
}
