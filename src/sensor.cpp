#include "sensor.h"
#include <math.h>

// ============================================================================
// Taupunkt-Berechnung (aus taupunkt_in.cpp)
// ============================================================================

float taupunkt(float t, float r) {
  float a, b;
  
  if (t >= 0) {
    a = 7.5;
    b = 237.3;
  } else if (t < 0) {
    a = 7.6;
    b = 240.7;
  }
  
  // Sättigungsdampfdruck in hPa
  float sdd = 6.1078 * pow(10, (a*t)/(b+t));
  
  // Dampfdruck in hPa
  float dd = sdd * (r/100);
  
  // v-Parameter
  float v = log10(dd/6.1078);
  
  // Taupunkttemperatur (°C)
  float tt = (b*v) / (a-v);
  return { tt };  
}

bool sollGelueftetWerden(float tempInnen, float humInnen, float tempAussen, float humAussen) {
  // Taupunkte berechnen
  float taupunktInnen = taupunkt(tempInnen, humInnen);
  float taupunktAussen = taupunkt(tempAussen, humAussen);
  
  // Taupunktdifferenz
  float deltaTP = taupunktInnen - taupunktAussen;
  
  // Lüftungslogik (vereinfacht - ohne Hysterese-Variable)
  static bool lueftungAn = false;
  
  if (deltaTP > (SCHALTmin + HYSTERESE)) {
    lueftungAn = true;
  } else if (deltaTP < SCHALTmin) {
    lueftungAn = false;
  }
  
  // Temperatur-Checks
  if (tempInnen < TEMP1_min) lueftungAn = false;
  if (tempAussen < TEMP2_min) lueftungAn = false;
  
  return lueftungAn;
}

// ============================================================================
// SensorManager Implementation
// ============================================================================

bool SensorManager::initIndoorSensor(int sdaPin, int sclPin) {
    // I2C konfigurieren
    Wire.begin(sdaPin, sclPin);
    Serial.printf("I2C konfiguriert: SDA=%d, SCL=%d\n", sdaPin, sclPin);

    // BME280 initialisieren
    Serial.println("BME280 Sensor wird initialisiert...");
    if (bme.begin(0x76)) {
        indoorSensorConnected = true;
        Serial.printf("✓ BME280 gefunden! Sensor ID: 0x%02X\n", bme.sensorID());
        return true;
    } else if (bme.begin(0x77)) {
        indoorSensorConnected = true;
        Serial.printf("✓ BME280 gefunden! Sensor ID: 0x%02X\n", bme.sensorID());
        return true;
    } else {
        indoorSensorConnected = false;
        Serial.println("✗ BME280 Sensor nicht gefunden!");
        return false;
    }
}

void SensorManager::updateIndoorSensor() {
    if (indoorSensorConnected) {
        float rawTemp = bme.readTemperature();
        float rawHumidity = bme.readHumidity();
        
        // Plausibilitätsprüfung: Temperaturen über 100°C deuten auf defekten Sensor hin
        if (rawTemp > MAX_TEMPERATURE_THRESHOLD) {
            indoorSensorConnected = false;
            Serial.printf("✗ Plausibilitätsprüfung: Temperatur %.2f°C > %.1f°C - Sensor als defekt markiert\n", 
                         rawTemp, MAX_TEMPERATURE_THRESHOLD);
        } else {
            indoorTemp = rawTemp;
            indoorHumidity = rawHumidity;
            
            // Taupunkt für Indoor berechnen
            taupunktInnen = taupunkt(indoorTemp, indoorHumidity);
        }
    } else {
        // Versuche Indoor-Sensor wieder zu erkennen (alle 30 Sekunden)
        static unsigned long lastIndoorRetry = 0;
        if (millis() - lastIndoorRetry > 30000) {
            float testTemp = bme.readTemperature();
            if (testTemp <= MAX_TEMPERATURE_THRESHOLD && testTemp > MIN_TEMPERATURE_THRESHOLD) {
                indoorSensorConnected = true;
                Serial.println("✓ Indoor-Sensor wieder erkannt!");
            }
            lastIndoorRetry = millis();
        }
    }
}

void SensorManager::updateOutdoorSensor(float temp, float humidity) {
    // Plausibilitätsprüfung für Outdoor-Sensordaten
    if (temp > MAX_TEMPERATURE_THRESHOLD) {
        // Sensor defekt (>100°C) - markiere als defekt
        outdoorSensorDefekt = true;
        Serial.printf("✗ Plausibilitätsprüfung Outdoor: Temperatur %.2f°C > %.1f°C - Sensor als defekt markiert\n", 
                     temp, MAX_TEMPERATURE_THRESHOLD);
        return;
    }
    
    // Sensor funktioniert (plausible Werte)
    outdoorSensorDefekt = false;
    lastOutdoorTemp = temp;
    lastOutdoorHumidity = humidity;
    lastOutdoorUpdate = millis();
    
    // Taupunkt für Outdoor berechnen
    if (isOutdoorConnected()) {
        taupunktAussen = taupunkt(lastOutdoorTemp, lastOutdoorHumidity);
    }
    
    // Debug-Ausgabe entfernt - wird im normalen Logging gezeigt
}

SensorData SensorManager::getIndoorData() {
    SensorData data;
    data.temperature = indoorTemp;
    data.humidity = indoorHumidity;
    data.dewPoint = taupunktInnen;
    data.connected = indoorSensorConnected;
    data.sensorDefect = !indoorSensorConnected;
    return data;
}

SensorData SensorManager::getOutdoorData() {
    SensorData data;
    data.temperature = lastOutdoorTemp;
    data.humidity = lastOutdoorHumidity;
    data.dewPoint = taupunktAussen;
    data.connected = isOutdoorConnected();
    data.sensorDefect = outdoorSensorDefekt;
    return data;
}

bool SensorManager::isIndoorConnected() {
    return indoorSensorConnected;
}

bool SensorManager::isOutdoorConnected() {
    return !isnan(lastOutdoorTemp) && millis() - lastOutdoorUpdate < OUTDOOR_TIMEOUT;
}

bool SensorManager::isOutdoorSensorDefekt() {
    return outdoorSensorDefekt;
}

float SensorManager::getIndoorDewPoint() {
    return taupunktInnen;
}

float SensorManager::getOutdoorDewPoint() {
    return taupunktAussen;
}

bool SensorManager::shouldVentilate() {
    // Lüftungsempfehlung berechnen (nur wenn beide Sensoren funktionieren)
    if (isIndoorConnected() && isOutdoorConnected()) {
        lueftungEmpfehlung = sollGelueftetWerden(indoorTemp, indoorHumidity, lastOutdoorTemp, lastOutdoorHumidity);
    } else {
        lueftungEmpfehlung = false; // Keine Empfehlung bei fehlenden Sensordaten
    }
    return lueftungEmpfehlung;
}

void SensorManager::logSensorStatus() {
    Serial.println("------------------------------------------");
    Serial.printf("Indoor: T=%.1f°C, H=%.1f%%, TP=%.1f°C, Connected: %s\n", 
                  indoorTemp, indoorHumidity, taupunktInnen, 
                  indoorSensorConnected ? "JA" : "NEIN");
    
    if (isOutdoorConnected() && !outdoorSensorDefekt) {
        Serial.printf("Outdoor: T=%.1f°C, H=%.1f%%, TP=%.1f°C, Connected: JA\n", 
                      lastOutdoorTemp, lastOutdoorHumidity, taupunktAussen);
    } else if (outdoorSensorDefekt) {
        Serial.println("Outdoor: Sensor defekt (>100°C)");
    } else {
        Serial.println("Outdoor: Offline (keine Daten)");
    }
    
    Serial.printf("Lüftungsempfehlung: %s\n", shouldVentilate() ? "JA" : "NEIN");
    Serial.println("------------------------------------------");
}
