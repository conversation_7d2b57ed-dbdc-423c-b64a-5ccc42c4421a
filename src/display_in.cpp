#include "display_in.h"
#include "FreeSans9pt7b.h"

void initDisplay(Adafruit_ST7789 &lcd) {
  lcd.init(170, 320); // Original: 170x320 (Hochformat)
  lcd.setRotation(1); // Keine Rotation - nutze es im Querformat
  lcd.fillScreen(ST77XX_BLACK);
  lcd.setTextColor(ST77XX_WHITE);
  lcd.setFont(&FreeSans9pt7b); // FreeSans Schriftart verwenden
}

void updateDisplay(Adafruit_ST7789 &lcd, 
                   float indoorTemp, float indoorHumidity, bool indoorSensorConnected,
                   float outdoorTemp, float outdoorHumidity, bool outdoorConnected, bool outdoorSensorDefekt,
                   float taupunktInnen, float taupunktAussen, bool lueftungEmpfehlung) {
  
  lcd.fillScreen(ST77XX_BLACK);

  // Zwei Spalten: Links DRINNEN, Rechts DRAUSSEN
  // DRINNEN - Links (0-160px Breite)
  lcd.setCursor(5, 20);
  lcd.setTextSize(1);
  lcd.setTextColor(ST77XX_CYAN);
  lcd.print("IN:");
  lcd.setTextColor(ST77XX_WHITE);
  
  lcd.setCursor(35, 30);
  lcd.setTextSize(2);
  if (indoorSensorConnected) {
    lcd.setFont(&FreeSans9pt7b);
    // Temperatur mit Komma als Dezimaltrennzeichen
    String tempStr = String(indoorTemp, 1);
    tempStr.replace('.', ',');
    lcd.print(tempStr);
    lcd.print("C");
    lcd.setCursor(2, 60);
    lcd.setTextSize(1);
    // FreeSans Font bereits in initDisplay() gesetzt
    lcd.print(indoorHumidity, 0);
    lcd.print("%   TP:");
    // Taupunkt mit Komma als Dezimaltrennzeichen
    String tpStr = String(taupunktInnen, 1);
    tpStr.replace('.', ',');
    lcd.print(tpStr);
    lcd.print("C");
  } else {
    lcd.setTextColor(ST77XX_RED);
    lcd.setTextSize(1); // Kleinere Schrift für Fehlermeldung
    lcd.setCursor(35, 20);
    lcd.print("KEIN");
    lcd.setCursor(35, 40);
    lcd.print("SENSOR");
    lcd.setTextColor(ST77XX_WHITE);
  }

  // DRAUSSEN - Rechts (160-320px Breite)
  lcd.setCursor(155, 20);
  lcd.setTextSize(1);
  lcd.setTextColor(ST77XX_CYAN);
  lcd.print("OUT:");
  lcd.setTextColor(ST77XX_WHITE);
  
  lcd.setCursor(200, 30);
  lcd.setTextSize(2);
  if (outdoorConnected) {
    String outTempStr = String(outdoorTemp, 1);
    outTempStr.replace('.', ',');
    lcd.print(outTempStr);
    lcd.print("C");
    lcd.setCursor(162, 60);
    lcd.setTextSize(1);
    lcd.print(outdoorHumidity, 0);
    lcd.print("%    TP:");
    // Außentaupunkt mit Komma als Dezimaltrennzeichen
    String outTpStr = String(taupunktAussen, 1);
    outTpStr.replace('.', ',');
    lcd.print(outTpStr);
    lcd.print("C");
  } else {
    lcd.setTextColor(ST77XX_RED);
    lcd.setTextSize(1); // Kleinere Schrift für Fehlermeldungen
    if (outdoorSensorDefekt) {
      // Sensor defekt (>100°C) -> KEIN SENSOR
      lcd.setCursor(205, 20);
      lcd.print("KEIN");
      lcd.setCursor(205, 40);
      lcd.print("SENSOR");
    } else {
      // Keine Daten empfangen -> OFFLINE
      lcd.setCursor(205, 20);
      lcd.print("OFF");
      lcd.setCursor(205, 40);
      lcd.print("LINE");
    }
    lcd.setTextColor(ST77XX_WHITE);
  }

  // LUEFTEN/NICHT LUEFTEN - Unten, zentriert und riesig
  lcd.setCursor(0, 110);
  lcd.setTextSize(2);
  
  if (indoorSensorConnected && outdoorConnected) {
    if (lueftungEmpfehlung) {
      lcd.setTextColor(ST77XX_GREEN);
      lcd.print("LUEFTEN");
      lcd.setTextColor(ST77XX_WHITE);
    } else {
      lcd.setTextColor(ST77XX_RED);
      lcd.print("NICHT LUEFTEN");
      lcd.setTextColor(ST77XX_WHITE);
    }
  } else {
    lcd.setTextSize(1);
    lcd.setTextColor(ST77XX_YELLOW);
    lcd.print("KEINE DATEN");
    lcd.setTextColor(ST77XX_WHITE);
  }
}
