#include <Arduino.h>
#include <WiFi.h>
#include <Adafruit_GFX.h>
#include <Adafruit_ST7789.h>
#include <ESPmDNS.h>
#include <WebServer.h>
#include <Wire.h>
#include <Adafruit_Sensor.h>
#include <Adafruit_BME280.h>
#include <NTPClient.h>
#include <WiFiUdp.h>
#include "config.h"
#include "display_in.h"
#include "sensor.h"
#include "async.h"

/*
 * WICHTIGE NOTIZEN:
 * - ESP32 drinnen mit Display und HTTP Server
 * - Empfängt Daten vom ESP32-C3 draußen
 * - KEINE IP-Adressen verwenden! Nur mDNS (.local) wegen Freifunk DHCP
 */


// Hardware-Konfiguration ist jetzt in config.h

Adafruit_ST7789 lcd = Adafruit_ST7789(LCD_CS, LCD_DC, LCD_RST);

// NTP Client für Zeitstempel
WiFiUDP ntpUDP;
NTPClient timeClient(ntpUDP, "pool.ntp.org", 3600, 60000); // UTC+1 (3600s), Update alle 60s

// BME280 Sensor wird jetzt über SensorManager verwaltet

// Sensor-Manager
SensorManager sensorManager;
WebServerManager webServerManager;

// Logging-Funktion für strukturierte Ausgabe
void logSensorData(float indoorTemp, float indoorHumidity, float indoorTaupunkt,
                   float outdoorTemp, float outdoorHumidity, float outdoorTaupunkt,
                   bool indoorConnected, bool outdoorConnected, bool outdoorSensorDefekt) {
  
  // Zeitstempel abrufen
  timeClient.update();
  String timestamp = timeClient.getFormattedTime();
  
  Serial.printf("Zeitstempel: %s\n", timestamp.c_str());
  
  // Indoor-Sensor Daten
  if (indoorConnected) {
    Serial.printf("DRINNEN:  T=%.1f°C, H=%.1f%%, TP=%.1f°C\n", 
                  indoorTemp, indoorHumidity, indoorTaupunkt);
  } else {
    Serial.println("DRINNEN:  Sensor defekt/fehlt");
  }
  
  // Outdoor-Sensor Daten
  if (outdoorConnected && !outdoorSensorDefekt) {
    Serial.printf("DRAUSSEN: T=%.1f°C, H=%.1f%%, TP=%.1f°C\n", 
                  outdoorTemp, outdoorHumidity, outdoorTaupunkt);
  } else if (outdoorSensorDefekt) {
    Serial.println("DRAUSSEN: Sensor defekt (>100°C)");
  } else {
    Serial.println("DRAUSSEN: Offline (keine Daten)");
  }
  
  Serial.println("==========================================");
  Serial.println();
}

void setup() {
  Serial.begin(SERIAL_BAUD_RATE);

  // Sensor-Manager initialisieren
  sensorManager.initIndoorSensor(SDA_PIN, SCL_PIN);

  // Display starten
  initDisplay(lcd);

  // WLAN verbinden
  lcd.setCursor(0, 0);
  lcd.setTextSize(2);
  lcd.print("Verbinde WLAN...");
  WiFi.begin(WIFI_SSID, WIFI_PASSWORD);
  while (WiFi.status() != WL_CONNECTED) {
    delay(WIFI_RETRY_DELAY);
    lcd.print(".");
  }
  lcd.fillScreen(ST77XX_BLACK);
  lcd.setCursor(0, 0);
  lcd.setTextSize(2);
  lcd.print("WLAN OK");
  lcd.setCursor(0, 25);
  lcd.setTextSize(1);
  lcd.print("IP: " + WiFi.localIP().toString());
  delay(2000);

  // mDNS starten (wichtig für Freifunk DHCP)
  if (!MDNS.begin(MDNS_HOSTNAME)) {
    Serial.println("✗ Fehler bei mDNS");
  } else {
    Serial.println("✓ mDNS gestartet: esp-inside.local");
  }

  // Web-Server konfigurieren
  webServerManager.setOutdoorDataCallback([&](float temp, float humidity) {
    sensorManager.updateOutdoorSensor(temp, humidity);
  });

  // SensorManager an WebServerManager übergeben für API-Endpunkt
  webServerManager.setSensorManager(&sensorManager);

  // Web-Server starten
  webServerManager.setupServer();
  Serial.println("✓ Web-Server gestartet");
  Serial.println("✓ Erreichbar unter: esp-inside.local");

  // NTP Client starten
  timeClient.begin();
  Serial.println("✓ NTP Client gestartet");
}

void loop() {
  // Sensor-Manager aktualisieren
  sensorManager.updateIndoorSensor();
  
  // Sensor-Daten abrufen
  SensorData indoorData = sensorManager.getIndoorData();
  SensorData outdoorData = sensorManager.getOutdoorData();
  bool lueftungEmpfehlung = sensorManager.shouldVentilate();

  // Display aktualisieren
  updateDisplay(lcd, indoorData.temperature, indoorData.humidity, indoorData.connected,
                outdoorData.temperature, outdoorData.humidity, outdoorData.connected, outdoorData.sensorDefect,
                indoorData.dewPoint, outdoorData.dewPoint, lueftungEmpfehlung);

  // Strukturiertes Logging alle 10 Sekunden
  static unsigned long lastLogTime = 0;
  if (millis() - lastLogTime >= LOG_INTERVAL) {
    logSensorData(indoorData.temperature, indoorData.humidity, indoorData.dewPoint,
                  outdoorData.temperature, outdoorData.humidity, outdoorData.dewPoint,
                  indoorData.connected, outdoorData.connected, outdoorData.sensorDefect);
    lastLogTime = millis();
  }

  delay(LOOP_DELAY);
}