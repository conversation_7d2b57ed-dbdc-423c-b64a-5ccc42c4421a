; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino
monitor_speed = 115200
board_build.filesystem = littlefs
lib_deps = 
	Adafruit/Adafruit GFX Library@^1.11.0
	Adafruit/Adafruit BusIO@^1.14.0
	adafruit/Adafruit ST7735 and ST7789 Library@^1.11.0
	adafruit/Adafruit BME280 Library@^2.2.2
	adafruit/Adafruit Unified Sensor@^1.1.9
	arduino-libraries/NTPClient@^3.2.0
	esp32async/AsyncTCP@^3.4.8
	esp32async/ESPAsyncWebServer@^3.8.1

[platformio]
default_envs = esp32dev