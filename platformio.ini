; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

; USB Upload Umgebung
[env:esp32c3]
platform = espressif32
board = esp32-c3-devkitc-02
framework = arduino
monitor_port = /dev/cu.usbmodem11201
monitor_speed = 115200
monitor_filters = default
upload_protocol = esptool
lib_deps = adafruit/Adafruit BME280 Library@^2.2.2
           adafruit/Adafruit Unified Sensor@^1.1.9

; OTA Upload Umgebung
[env:esp32c3_ota]
platform = espressif32
board = esp32-c3-devkitc-02
framework = arduino
monitor_speed = 115200
monitor_filters = default
upload_protocol = espota
upload_port = esp-outdoor.local
upload_flags = --auth=ota123
lib_deps = adafruit/Adafruit BME280 Library@^2.2.2
           adafruit/Adafruit Unified Sensor@^1.1.9