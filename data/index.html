<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESP32 Lüftungssystem</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        
        .header {
            background-color: #333;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .content {
            padding: 20px;
        }
        
        .sensor-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .sensor-box {
            border: 1px solid #ddd;
            padding: 15px;
            background-color: #fafafa;
        }
        
        .sensor-title {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 15px;
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
        }
        
        .data-row {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
        }
        
        .data-label {
            font-weight: normal;
        }
        
        .data-value {
            font-weight: bold;
        }
        
        .ventilation-status {
            text-align: center;
            padding: 20px;
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            margin-bottom: 20px;
        }
        
        .ventilation-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .ventilation-recommendation {
            font-size: 20px;
            font-weight: bold;
            padding: 10px;
            border: 2px solid;
        }
        
        .ventilation-recommendation.yes {
            background-color: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        
        .ventilation-recommendation.no {
            background-color: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
        
        .timestamp {
            text-align: center;
            padding: 10px;
            background-color: #e9ecef;
            font-size: 12px;
            color: #6c757d;
        }
        
        .offline {
            color: #6c757d;
            font-style: italic;
        }
        
        @media (max-width: 600px) {
            .sensor-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>ESP32 Lüftungssystem</h1>
        </div>
        
        <div class="content">
            <div class="sensor-grid">
                <!-- DRINNEN -->
                <div class="sensor-box">
                    <div class="sensor-title">DRINNEN</div>
                    
                    <div class="data-row">
                        <span class="data-label">Temperatur:</span>
                        <span class="data-value" id="indoor-temp">-- °C</span>
                    </div>
                    
                    <div class="data-row">
                        <span class="data-label">Luftfeuchtigkeit:</span>
                        <span class="data-value" id="indoor-humidity">-- %</span>
                    </div>
                    
                    <div class="data-row">
                        <span class="data-label">Taupunkt:</span>
                        <span class="data-value" id="indoor-dewpoint">-- °C</span>
                    </div>
                    
                    <div class="data-row">
                        <span class="data-label">Status:</span>
                        <span class="data-value" id="indoor-status">--</span>
                    </div>
                </div>
                
                <!-- DRAUSSEN -->
                <div class="sensor-box">
                    <div class="sensor-title">DRAUSSEN</div>
                    
                    <div class="data-row">
                        <span class="data-label">Temperatur:</span>
                        <span class="data-value" id="outdoor-temp">-- °C</span>
                    </div>
                    
                    <div class="data-row">
                        <span class="data-label">Luftfeuchtigkeit:</span>
                        <span class="data-value" id="outdoor-humidity">-- %</span>
                    </div>
                    
                    <div class="data-row">
                        <span class="data-label">Taupunkt:</span>
                        <span class="data-value" id="outdoor-dewpoint">-- °C</span>
                    </div>
                    
                    <div class="data-row">
                        <span class="data-label">Status:</span>
                        <span class="data-value" id="outdoor-status">--</span>
                    </div>
                </div>
            </div>
            
            <!-- LÜFTUNGSEMPFEHLUNG -->
            <div class="ventilation-status">
                <div class="ventilation-title">Lüftungsempfehlung</div>
                <div class="ventilation-recommendation" id="ventilation-recommendation">
                    -- Daten werden geladen --
                </div>
            </div>
        </div>
        
        <div class="timestamp" id="timestamp">
            Letzte Aktualisierung: --
        </div>
    </div>
    
    <script>
        function updateIndoorData(temp, humidity, dewpoint, connected) {
            document.getElementById('indoor-temp').textContent = connected ? temp.toFixed(1) + ' °C' : '-- °C';
            document.getElementById('indoor-humidity').textContent = connected ? humidity.toFixed(0) + ' %' : '-- %';
            document.getElementById('indoor-dewpoint').textContent = connected ? dewpoint.toFixed(1) + ' °C' : '-- °C';
            document.getElementById('indoor-status').textContent = connected ? 'Verbunden' : 'Offline';
            document.getElementById('indoor-status').className = connected ? 'data-value' : 'data-value offline';
        }
        
        function updateOutdoorData(temp, humidity, dewpoint, connected) {
            document.getElementById('outdoor-temp').textContent = connected ? temp.toFixed(1) + ' °C' : '-- °C';
            document.getElementById('outdoor-humidity').textContent = connected ? humidity.toFixed(0) + ' %' : '-- %';
            document.getElementById('outdoor-dewpoint').textContent = connected ? dewpoint.toFixed(1) + ' °C' : '-- °C';
            document.getElementById('outdoor-status').textContent = connected ? 'Verbunden' : 'Offline';
            document.getElementById('outdoor-status').className = connected ? 'data-value' : 'data-value offline';
        }
        
        function updateVentilationRecommendation(shouldVentilate) {
            const element = document.getElementById('ventilation-recommendation');
            if (shouldVentilate) {
                element.textContent = 'LUEFTEN';
                element.className = 'ventilation-recommendation yes';
            } else {
                element.textContent = 'NICHT LUEFTEN';
                element.className = 'ventilation-recommendation no';
            }
        }
        
        function updateTimestamp() {
            const now = new Date();
            document.getElementById('timestamp').textContent = 'Letzte Aktualisierung: ' + now.toLocaleString('de-DE');
        }
        
        updateTimestamp();

        // Echte Daten von der API laden
        function loadSensorData() {
            fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    // Indoor-Daten aktualisieren
                    updateIndoorData(
                        data.indoor.temperature,
                        data.indoor.humidity,
                        data.indoor.dewPoint,
                        data.indoor.connected && !data.indoor.sensorDefect
                    );

                    // Outdoor-Daten aktualisieren
                    updateOutdoorData(
                        data.outdoor.temperature,
                        data.outdoor.humidity,
                        data.outdoor.dewPoint,
                        data.outdoor.connected && !data.outdoor.sensorDefect
                    );

                    // Lüftungsempfehlung aktualisieren
                    updateVentilationRecommendation(data.shouldVentilate);

                    // Zeitstempel aktualisieren
                    updateTimestamp();
                })
                .catch(error => {
                    console.error('Fehler beim Laden der Sensor-Daten:', error);
                    // Fallback: Offline-Status anzeigen
                    updateIndoorData(0, 0, 0, false);
                    updateOutdoorData(0, 0, 0, false);
                    document.getElementById('ventilation-recommendation').textContent = 'KEINE DATEN VERFÜGBAR';
                    document.getElementById('ventilation-recommendation').className = 'ventilation-recommendation no';
                });
        }

        // Initiales Laden der Daten
        loadSensorData();

        // Automatische Aktualisierung alle 5 Sekunden
        setInterval(loadSensorData, 5000);
    </script>
</body>
</html>