#ifndef ASYNC_H
#define ASYNC_H

#include <Arduino.h>
#include <AsyncTCP.h>
#include <ESPAsyncWebServer.h>
#include <LittleFS.h>
#include <functional>

// Forward declaration für SensorManager
class SensorManager;

class WebServerManager {
private:
    AsyncWebServer server{80};
    std::function<void(float, float)> outdoorDataCallback;
    SensorManager* sensorManagerPtr = nullptr;

public:
    // Initialisierung
    void setupServer();
    void handleClient();
    void setOutdoorDataCallback(std::function<void(float, float)> callback);
    void setSensorManager(SensorManager* manager);

    // Handler für verschiedene Endpunkte
    void handleSensorEndpoint(AsyncWebServerRequest *request);
    void handleRootEndpoint(AsyncWebServerRequest *request);
    void handleApiDataEndpoint(AsyncWebServerRequest *request);
    void handleNotFound(AsyncWebServerRequest *request);

    // Utility-Funktionen
    void startLittleFS();
    String getContentType(String filename);
};

#endif
