#ifndef SENSOR_H
#define SENSOR_H

#include <Arduino.h>
#include <Wire.h>
#include <Adafruit_Sensor.h>
#include <Adafruit_BME280.h>
#include "config.h"

// Taupunkt-Berechnung und Lüftungslogik
float taupunkt(float temperatur, float luftfeuchtigkeit);
bool sollGelueftetWerden(float tempInnen, float humInnen, float tempAussen, float humAussen);

// Sensor-Datenstruktur
struct SensorData {
    float temperature;
    float humidity;
    float dewPoint;
    bool connected;
    bool sensorDefect;
};

class SensorManager {
private:
    Adafruit_BME280 bme;
    
    // Indoor sensor
    float indoorTemp = 0;
    float indoorHumidity = 0;
    bool indoorSensorConnected = false;
    
    // Outdoor sensor
    float lastOutdoorTemp = NAN;
    float lastOutdoorHumidity = NAN;
    bool outdoorSensorDefekt = false;
    unsigned long lastOutdoorUpdate = 0;
    
    // Taupunkt-Berechnungen
    float taupunktInnen = 0;
    float taupunktAussen = 0;
    bool lueftungEmpfehlung = false;

public:
    // Initialisierung
    bool initIndoorSensor(int sdaPin, int sclPin);
    
    // Indoor-Sensor
    void updateIndoorSensor();
    SensorData getIndoorData();
    bool isIndoorConnected();
    
    // Outdoor-Sensor
    void updateOutdoorSensor(float temp, float humidity);
    SensorData getOutdoorData();
    bool isOutdoorConnected();
    bool isOutdoorSensorDefekt();
    
    // Taupunkt und Lüftung
    float getIndoorDewPoint();
    float getOutdoorDewPoint();
    bool shouldVentilate();
    
    // Status-Logging
    void logSensorStatus();
};

#endif
