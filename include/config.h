#ifndef CONFIG_H
#define CONFIG_H

// ============================================================================
// Hardware-Konfiguration
// ============================================================================

// Display-Pins (ST7789)
#define LCD_MOSI 23
#define LCD_SCLK 18
#define LCD_CS   15
#define LCD_DC   2
#define LCD_RST  4
#define LCD_BLK  32

// I2C Pins für BME280 Sensor
#define SDA_PIN 12
#define SCL_PIN 13

// ============================================================================
// Sensor-Konfiguration
// ============================================================================

// Temperatur-Schwellwerte für Plausibilitätsprüfung
#define MAX_TEMPERATURE_THRESHOLD 100.0  // Temperatur über 100°C = Sensor defekt
#define MIN_TEMPERATURE_THRESHOLD -50.0  // Temperatur unter -50°C = unwahrscheinlich

// Timeout-Konfiguration
#define OUTDOOR_TIMEOUT 15000            // 15 Sekunden Timeout für Außensensor
#define SENSOR_RETRY_INTERVAL 30000      // 30 Sekunden für Sensor-Wiedererkennung

// ============================================================================
// Taupunkt-Konfiguration
// ============================================================================

#define SCHALTmin   5.0   // minimaler Taupunktunterschied, bei dem das Relais schaltet
#define HYSTERESE   1.0   // Abstand von Ein- und Ausschaltpunkt
#define TEMP1_min  10.0   // Minimale Innentemperatur, bei der die Lüftung aktiviert wird
#define TEMP2_min -10.0   // Minimale Außentemperatur, bei der die Lüftung aktiviert wird

// ============================================================================
// Timing-Konfiguration
// ============================================================================

#define LOG_INTERVAL 10000      // 10 Sekunden zwischen Logging-Ausgaben
#define LOOP_DELAY 5000         // 5 Sekunden zwischen Loop-Durchläufen
#define WIFI_RETRY_DELAY 500    // 500ms zwischen WLAN-Verbindungsversuchen

// ============================================================================
// WiFi-Konfiguration
// ============================================================================

#define WIFI_SSID "Freifunk"
#define WIFI_PASSWORD ""
#define MDNS_HOSTNAME "esp-inside"

// ============================================================================
// NTP-Konfiguration
// ============================================================================

#define NTP_SERVER "pool.ntp.org"
#define NTP_OFFSET 3600         // UTC+1 (3600 Sekunden)
#define NTP_UPDATE_INTERVAL 60000  // Update alle 60 Sekunden

// ============================================================================
// Web-Server-Konfiguration
// ============================================================================

#define WEB_SERVER_PORT 80
#define SENSOR_ENDPOINT "/sensor"

// ============================================================================
// Display-Konfiguration
// ============================================================================

#define DISPLAY_WIDTH 170
#define DISPLAY_HEIGHT 320
#define DISPLAY_ROTATION 1

// ============================================================================
// Serial-Konfiguration
// ============================================================================

#define SERIAL_BAUD_RATE 115200

#endif
